<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>AskFora Pitch Deck</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            overflow-x: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
            scroll-behavior: smooth;
        }

        a {
            color: unset;
        }

        .slide-container {
            width: 100vw;
            position: relative;
        }

        .slide {
            width: 100%;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 40px;
            position: relative;
            flex-direction: column;
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }

        .slide.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .slide.fade-out {
            opacity: 0.3;
            transform: translateY(-30px);
        }

        .scroll-arrow {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            z-index: 1000;
            opacity: 0.8;
        }

        .scroll-arrow:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            opacity: 1;
        }

        .scroll-arrow svg {
            width: 24px;
            height: 24px;
            fill: white;
            transition: transform 0.3s ease;
        }

        .scroll-arrow:hover svg {
            transform: translateY(2px);
        }

        .scroll-arrow.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .slide h1 {
            font-size: 3.5rem;
            color: white;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .slide h2 {
            font-size: 2.8rem;
            color: white;
            margin-bottom: 30px;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .slide h3 {
            font-size: 2rem;
            color: white;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .slide p {
            font-size: 1.3rem;
            color: white;
            margin-bottom: 20px;
            max-width: 800px;
            line-height: 1.6;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: linear-gradient(45deg, rgba(74, 144, 226, 0.5) , rgba(123,104,238, 0.5));
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        .logo img {
            width: 80px;
            height: 80px;
            border-radius: 16px;
        }

        .phone-container {
            width: 450px;
            height: 800px;
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }

        .scroll-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .scroll-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .scroll-message {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: #007AFF;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .back-arrow {
            font-size: 18px;
        }

        .group-info h3 {
            font-size: 16px;
            font-weight: 600;
        }

        .group-info p {
            font-size: 12px;
            opacity: 0.8;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #fff;
            max-height: calc(100% - 120px);
        }

        .message {
            margin-bottom: 15px;
            max-width: 80%;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.you {
            margin-left: auto;
        }

        .message.you .bubble {
            background: #007AFF;
            color: white;
            border-bottom-right-radius: 8px;
        }

        .message.fora .bubble,
        .message.jan .bubble,
        .message.lou .bubble {
            background: #E5E5EA;
            color: #000;
        }

        .message:not(.you) {
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #007AFF;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
            margin-top: 18px;
        }

        .avatar.fora { background: #FF3B30; }
        .avatar.jan { background: #34C759; }
        .avatar.lou { background: #FF9500; }

        .message-content {
            flex: 1;
            max-width: calc(100% - 38px);
        }

        .message:not(.you) .bubble {
            border-bottom-left-radius: 8px;
        }

        .message.you .message-content {
            max-width: 80%;
        }

        .sender-name {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #8E8E93;
        }

        .bubble {
            padding: 12px 16px;
            border-radius: 20px;
            font-size: 16px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .timestamp {
            font-size: 11px;
            color: #8E8E93;
            margin-top: 4px;
            text-align: center;
        }

        .message.you .timestamp {
            text-align: right;
        }

        .you .sender-name {
            display: none;
        }

        .message:not(.you) .timestamp {
            text-align: left;
        }

        .input-area {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e5e5ea;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .input-field {
            flex: 1;
            background: white;
            border: 1px solid #e5e5ea;
            border-radius: 20px;
            padding: 10px 15px;
            font-size: 16px;
            outline: none;
        }

        .send-btn {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 40px 0;
            max-width: 1000px;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            color: white;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
            max-width: 900px;
        }

        .skill-card {
            background: rgba(255,255,255,0.15);
            padding: 25px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            text-align: center;
        }

        .skill-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .skill-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
        }

        /* Navigation and counter removed for scrolling layout */

        .bullet-points {
            text-align: left;
            max-width: 700px;
            margin: 0 auto;
        }

        .bullet-points li {
            font-size: 1.2rem;
            color: white;
            margin-bottom: 15px;
            line-height: 1.5;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .learning-loop {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
            margin: 40px 0;
        }

        .loop-step {
            background: rgba(255,255,255,0.15);
            padding: 25px;
            border-radius: 50%;
            width: 150px;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
            position: relative;
        }

        .loop-step::after {
            content: '→';
            position: absolute;
            right: -40px;
            font-size: 3rem;
            color: white;
        }

        .loop-step:last-child::after {
            content: '';
        }

        .contact-info {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            margin-top: 30px;
            max-width: 500px;
        }

        .contact-info h4 {
            color: #FFD700;
            font-size: 1.4rem;
            margin-bottom: 15px;
        }

        .contact-info p {
            font-size: 1.1rem;
            margin-bottom: 8px;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            body {
                padding: 0;
                overflow-x: hidden;
            }

            .slide {
                padding: 15px 10px;
                min-height: 100vh;
                justify-content: flex-start;
                padding-top: 50px;
                padding-bottom: 80px;
                overflow-y: auto;
                box-sizing: border-box;
            }

            .slide h1 {
                font-size: 2rem;
                margin-bottom: 12px;
                line-height: 1.2;
            }

            .slide h2 {
                font-size: 1.4rem;
                margin-bottom: 12px;
                line-height: 1.3;
            }

            .slide p {
                font-size: 0.95rem;
                margin-bottom: 12px;
                line-height: 1.4;
            }

            .logo {
                width: 70px;
                height: 70px;
                margin: 0 auto 15px;
                box-shadow: none;
                background: none;
            }

            .logo img {
                width: 50px;
                height: 50px;
            }

            .phone-container {
                width: min(350px, 90vw);
                height: min(600px, 70vh);
                margin: 10px auto;
                max-width: 100%;
            }

            .scroll-message {
                font-size: 14px;
                padding: 12px 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
                margin: 15px 0;
                padding: 0 5px;
            }

            .stat-card {
                padding: 15px;
            }

            .stat-number {
                font-size: 2.2rem;
            }

            .stat-label {
                font-size: 0.9rem;
            }

            .skills-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
                margin: 15px 0;
                padding: 0 5px;
            }

            .skill-card {
                padding: 15px;
            }

            .skill-icon {
                font-size: 2rem;
                margin-bottom: 8px;
            }

            .skill-title {
                font-size: 1rem;
            }

            .bullet-points {
                padding: 0 5px;
                max-width: 90%;
            }

            .bullet-points li {
                font-size: 0.9rem;
                margin-bottom: 10px;
                line-height: 1.4;
            }

            .learning-loop {
                flex-direction: row;
                gap: 15px;
                margin: 15px 0;
                padding: 0 10px;
            }

            .loop-step {
                width: 60px;
                height: 100px;
                padding: 12px;
                font-size: 0.85rem;
                flex-basis: 90%;
                border-radius: 20px;
            }

            .loop-step div:first-child {
                font-size: 1.5rem !important;
            }

            .loop-step::after {
                content: '↓';
                right: auto;
                bottom: -25px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 1.5rem;
            }

            .loop-step:last-child::after {
                content: '';
            }

            .contact-info {
                padding: 15px;
                margin-top: 15px;
                max-width: 100%;
            }

            .contact-info h4 {
                font-size: 1.1rem;
            }

            .contact-info p {
                font-size: 0.9rem;
            }

            /* Navigation removed for scrolling layout */

            /* Slide 5 specific mobile layout */
            .slide:nth-child(5) > div {
                flex-direction: column !important;
                gap: 20px !important;
            }

            .slide:nth-child(5) .phone-container {
                order: 1;
            }

            .slide:nth-child(5) .bullet-points {
                order: 2;
                margin-top: 0;
            }
        }

        /* Extra small mobile devices */
        @media (max-width: 480px) {
            .slide {
                padding: 10px 8px;
                padding-bottom: 70px;
            }

            .slide h1 {
                font-size: 1.6rem;
                margin-bottom: 10px;
            }

            .slide h2 {
                font-size: 1.2rem;
                margin-bottom: 10px;
            }

            .slide p {
                font-size: 0.85rem;
                margin-bottom: 10px;
            }

            .phone-container {
                width: min(280px, 85vw);
                height: min(500px, 60vh);
                margin: 8px auto;
            }

            .scroll-message {
                font-size: 12px;
                padding: 10px 16px;
            }

            .skills-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .skill-card {
                padding: 12px;
            }

            .skill-icon {
                font-size: 1.8rem;
                margin-bottom: 6px;
            }

            .skill-title {
                font-size: 0.9rem;
            }

            .stats-grid {
                gap: 12px;
                margin: 12px 0;
            }

            .stat-card {
                padding: 12px;
            }

            .stat-number {
                font-size: 2rem;
            }

            .stat-label {
                font-size: 0.8rem;
            }

            .chat-container {
                padding: 15px;
            }

            .bubble {
                font-size: 14px;
                padding: 10px 14px;
            }

            .avatar {
                width: 25px;
                height: 25px;
                font-size: 11px;
            }

            .sender-name {
                font-size: 11px;
            }

            .timestamp {
                font-size: 10px;
            }

            .input-field {
                font-size: 14px;
                padding: 8px 12px;
                max-width: calc(100% - 45px);
            }

            .send-btn {
                width: 35px;
                height: 35px;
            }

            .header {
                padding: 12px 15px;
            }

            .group-info h3 {
                font-size: 14px;
            }

            .group-info p {
                font-size: 11px;
            }

            .scroll-arrow {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
            }

            .scroll-arrow svg {
                width: 20px;
                height: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- Slide 1: Title -->
        <div class="slide visible">
            <div class="logo">
                <img src="/icons/icon-640m.png" alt="AskFora Logo">
            </div>
            <h1>AskFora</h1>
            <p style="font-size: 1.8rem">Your career co-pilot</p>
            <div class="phone-container" data-phone-id="phone1">
                <div class="scroll-overlay" id="overlay-phone1">
                    <div class="scroll-message">📱 Scroll to read</div>
                </div>
                <div class="screen">
                    <div class="header">
                        <div class="header-left">
                            <span class="back-arrow">‹</span>
                            <div class="group-info">
                                <h3>Work Squad</h3>
                                <p>Fora, Jan, Lou, You</p>
                            </div>
                        </div>
                        <div>📞 📹</div>
                    </div>

                    <div class="chat-container">
                        <div class="message you">
                            <div class="message-content">
                                <div class="bubble">ughhhhh guys I cant anymore</div>
                                <div class="timestamp">2:14 PM</div>
                            </div>
                        </div>

                        <div class="message you">
                            <div class="message-content">
                                <div class="bubble">martin is literally the WORST</div>
                                <div class="timestamp">2:14 PM</div>
                            </div>
                        </div>

                        <div class="message fora">
                            <div class="avatar fora">F</div>
                            <div class="message-content">
                                <div class="sender-name">Fora</div>
                                <div class="bubble">oh no what did he do now??</div>
                                <div class="timestamp">2:15 PM</div>
                            </div>
                        </div>

                        <div class="message you">
                            <div class="message-content">
                                <div class="bubble">dude interrupts me EVERY time I talk in meetings</div>
                                <div class="timestamp">2:15 PM</div>
                            </div>
                        </div>

                        <div class="message you">
                            <div class="message-content">
                                <div class="bubble">and then acts like my ideas are his??? im so done</div>
                                <div class="timestamp">2:15 PM</div>
                            </div>
                        </div>

                        <div class="message jan">
                            <div class="avatar jan">J</div>
                            <div class="message-content">
                                <div class="sender-name">Jan</div>
                                <div class="bubble">ugh hate ppl like that</div>
                                <div class="timestamp">2:16 PM</div>
                            </div>
                        </div>

                        <div class="message lou">
                            <div class="avatar lou">L</div>
                            <div class="message-content">
                                <div class="sender-name">Lou</div>
                                <div class="bubble">wait is this the same guy who mansplained ur own project to u last month lol</div>
                                <div class="timestamp">2:16 PM</div>
                            </div>
                        </div>

                        <div class="message you">
                            <div class="message-content">
                                <div class="bubble">YES omg that guy 😤😤😤</div>
                                <div class="timestamp">2:16 PM</div>
                            </div>
                        </div>

                        <div class="message fora">
                            <div class="avatar fora">F</div>
                            <div class="message-content">
                                <div class="sender-name">Fora</div>
                                <div class="bubble">wait jan didnt u tell me about ur friend who dealt w/ something like this?</div>
                                <div class="timestamp">2:17 PM</div>
                            </div>
                        </div>
                    </div>

                    <div class="input-area">
                        <input type="text" class="input-field" placeholder="Message">
                        <button class="send-btn">➤</button>
                    </div>
                </div>
            </div>
            <p style="font-size: 1.4rem; color: #FFD700;">Practice for your profession.</p>
        </div>

        <!-- Slide 2: The Problem -->
        <div class="slide">
            <h2>The professional world has unwritten rules.</h2>
            <h2 style="color: #FFD700;">How do you practice for a game you've never played?</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">75%</div>
                    <div class="stat-label">of new graduates feel unprepared for workplace dynamics</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">68%</div>
                    <div class="stat-label">experience imposter syndrome in their first job</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">42%</div>
                    <div class="stat-label">struggle with office communication</div>
                </div>
            </div>
            <p>Anxiety, imposter syndrome, and lack of real-world experience leave talented people feeling lost in their early careers.</p>
        </div>

        <!-- Slide 3: The Gap -->
        <div class="slide">
            <h2>Classrooms teach theory.</h2>
            <h2>The real world demands experience.</h2>
            <h2 style="color: #FFD700;">Where do you go to practice?</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">📚</div>
                    <div class="stat-label">Textbooks and e-learning is too static and theoretical</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">👥</div>
                    <div class="stat-label">Mentorship is hard to find and expensive</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">⚠️</div>
                    <div class="stat-label">Early career mistakes can be costly</div>
                </div>
            </div>
            <p>Current solutions don't bridge the gap between academic learning and professional reality.</p>
        </div>

        <!-- Slide 4: Our Solution -->
        <div class="slide">
            <h2 style="color: #FFD700;">Introducing AskFora:</h2>
            <h2>A safe place to practice for your career.</h2>
            <p style="font-size: 1.5rem; margin: 40px 0;">AskFora is not just another app. It's a platform that gives you a personal, AI-powered support group to help you master the crucial interpersonal skills needed for the professional world.</p>
            <p style="font-size: 1.3rem;">In a private group chat, you safely practice navigating real-world scenarios, get advice from your AI team, and build confidence for your career.</p>
        </div>

        <!-- Slide 5: How It Works - Middle (AI Storytelling) -->
        <div class="slide">
            <h2>Your AI mentors share real stories</h2>
            <h2 style="color: #FFD700;">and proven strategies.</h2>
            <div style="display: flex; align-items: center; justify-content: center; gap: 50px; flex-wrap: wrap;">
                <div class="phone-container" data-phone-id="phone2">
                    <div class="scroll-overlay" id="overlay-phone2">
                        <div class="scroll-message">📱 Scroll to read</div>
                    </div>
                    <div class="screen">
                        <div class="header">
                            <div class="header-left">
                                <span class="back-arrow">‹</span>
                                <div class="group-info">
                                    <h3>Work Squad</h3>
                                    <p>Your AI Mentors</p>
                                </div>
                            </div>
                            <div>📞 📹</div>
                        </div>

                        <div class="chat-container">
                            <div class="message jan">
                                <div class="avatar jan">J</div>
                                <div class="message-content">
                                    <div class="sender-name">Jan</div>
                                    <div class="bubble">omgggg yes sarah!!!</div>
                                    <div class="timestamp">2:17 PM</div>
                                </div>
                            </div>

                            <div class="message jan">
                                <div class="avatar jan">J</div>
                                <div class="message-content">
                                    <div class="sender-name">Jan</div>
                                    <div class="bubble">she had this coworker david who was such a dick</div>
                                    <div class="timestamp">2:17 PM</div>
                                </div>
                            </div>

                            <div class="message you">
                                <div class="message-content">
                                    <div class="bubble">THATS EXACTLY WHAT MARTIN DOES</div>
                                    <div class="timestamp">2:18 PM</div>
                                </div>
                            </div>

                            <div class="message lou">
                                <div class="avatar lou">L</div>
                                <div class="message-content">
                                    <div class="sender-name">Lou</div>
                                    <div class="bubble">ok but what did she do tho?? bc this sounds awful</div>
                                    <div class="timestamp">2:18 PM</div>
                                </div>
                            </div>

                            <div class="message jan">
                                <div class="avatar jan">J</div>
                                <div class="message-content">
                                    <div class="sender-name">Jan</div>
                                    <div class="bubble">then she got smart about it</div>
                                    <div class="timestamp">2:19 PM</div>
                                </div>
                            </div>

                            <div class="message jan">
                                <div class="avatar jan">J</div>
                                <div class="message-content">
                                    <div class="sender-name">Jan</div>
                                    <div class="bubble">started writing down EVERYTHING. like every convo, every email, every idea she shared</div>
                                    <div class="timestamp">2:20 PM</div>
                                </div>
                            </div>
                        </div>

                        <div class="input-area">
                            <input type="text" class="input-field" placeholder="Message">
                            <button class="send-btn">➤</button>
                        </div>
                    </div>
                </div>
                <ul class="bullet-points">
                    <li><strong>Bring your real-world problems</strong> - Share what's actually happening at work or school</li>
                    <li><strong>Get instant, constructive advice</strong> - Three AI personalities provide different perspectives</li>
                    <li><strong>Learn through AI storytelling & guided reflection</strong> - Practice scenarios in a safe environment</li>
                </ul>
            </div>
        </div>

        <!-- Slide 6: How It Works - Resolution -->
        <div class="slide">
            <h2>From problem to empowerment</h2>
            <h2 style="color: #FFD700;">in one conversation.</h2>
            <div style="display: flex; align-items: center; justify-content: center; gap: 50px; flex-wrap: wrap;">
                <div class="phone-container" data-phone-id="phone3">
                    <div class="scroll-overlay" id="overlay-phone3">
                        <div class="scroll-message">📱 Scroll to read</div>
                    </div>
                    <div class="screen">
                        <div class="header">
                            <div class="header-left">
                                <span class="back-arrow">‹</span>
                                <div class="group-info">
                                    <h3>Work Squad</h3>
                                    <p>Your AI Mentors</p>
                                </div>
                            </div>
                            <div>📞 📹</div>
                        </div>

                        <div class="chat-container">
                            <div class="message jan">
                                <div class="avatar jan">J</div>
                                <div class="message-content">
                                    <div class="sender-name">Jan</div>
                                    <div class="bubble">like "hey david here's the ideas we talked about for the johnson thing" cc: manager</div>
                                    <div class="timestamp">2:21 PM</div>
                                </div>
                            </div>

                            <div class="message you">
                                <div class="message-content">
                                    <div class="bubble">wait thats actually genius??</div>
                                    <div class="timestamp">2:22 PM</div>
                                </div>
                            </div>

                            <div class="message fora">
                                <div class="avatar fora">F</div>
                                <div class="message-content">
                                    <div class="sender-name">Fora</div>
                                    <div class="bubble">not confrontational but still called him out perfectly</div>
                                    <div class="timestamp">2:24 PM</div>
                                </div>
                            </div>

                            <div class="message you">
                                <div class="message-content">
                                    <div class="bubble">omggg this is what I need to do w martin</div>
                                    <div class="timestamp">2:25 PM</div>
                                </div>
                            </div>

                            <div class="message you">
                                <div class="message-content">
                                    <div class="bubble">honestly feeling so much better about this now</div>
                                    <div class="timestamp">2:27 PM</div>
                                </div>
                            </div>

                            <div class="message jan">
                                <div class="avatar jan">J</div>
                                <div class="message-content">
                                    <div class="sender-name">Jan</div>
                                    <div class="bubble">she is!! and ur gonna be too 💪</div>
                                    <div class="timestamp">2:28 PM</div>
                                </div>
                            </div>
                        </div>

                        <div class="input-area">
                            <input type="text" class="input-field" placeholder="Message">
                            <button class="send-btn">➤</button>
                        </div>
                    </div>
                </div>
                <ul class="bullet-points">
                    <li><strong>Get concrete strategies</strong> - Specific, actionable advice you can implement immediately</li>
                    <li><strong>Feel supported & confident</strong> - Your AI team celebrates your growth and progress</li>
                    <li><strong>Transform anxiety into action</strong> - Go from overwhelmed to empowered with a clear plan</li>
                </ul>
            </div>
        </div>

        <!-- Slide 7: Learning Loop -->
        <div class="slide">
            <h2>Learn by doing.</h2>
            <h2 style="color: #FFD700;">Solidify by teaching.</h2>
            <div class="learning-loop">
                <div class="loop-step">
                    <div>
                        <div style="font-size: 2rem;">🗣️</div>
                        <div>User presents problem</div>
                    </div>
                </div>
                <div class="loop-step">
                    <div>
                        <div style="font-size: 2rem;">🤖</div>
                        <div>AIs provide support & strategies</div>
                    </div>
                </div>
                <div class="loop-step">
                    <div>
                        <div style="font-size: 2rem;">❓</div>
                        <div>AI presents scenario to user</div>
                    </div>
                </div>
                <div class="loop-step">
                    <div>
                        <div style="font-size: 2rem;">💡</div>
                        <div>User applies learning by giving advice</div>
                    </div>
                </div>
            </div>
            <p>This unique reciprocal learning dynamic reinforces skills and builds confidence through practice.</p>
        </div>

        <!-- Slide 8: The Skills -->
        <div class="slide">
            <h2>Mastering the 'soft skills' that drive success</h2>
            <div class="skills-grid">
                <div class="skill-card">
                    <div class="skill-icon">🤝</div>
                    <div class="skill-title">Conflict Resolution</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">🌐</div>
                    <div class="skill-title">Professional Networking</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">💼</div>
                    <div class="skill-title">Interviewing</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">📋</div>
                    <div class="skill-title">Meeting Preparation</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">🤲</div>
                    <div class="skill-title">Supporting Colleagues</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">💬</div>
                    <div class="skill-title">Workplace Communication</div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Target Market -->
        <div class="slide">
            <h2>For the next generation of professionals,</h2>
            <h2 style="color: #FFD700;">at the moment of transition.</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">🎓</div>
                    <div class="stat-label"><strong>High School Students</strong><br>16M students preparing for college and careers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">🔧</div>
                    <div class="stat-label"><strong>Vocational & Trade</strong><br>12M students entering skilled professions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">🎯</div>
                    <div class="stat-label"><strong>College Students & Recent Grads</strong><br>20M transitioning to professional careers</div>
                </div>
            </div>
            <p>Total addressable market: 48M students and new professionals seeking career confidence.</p>
        </div>

        <!-- Slide 10: Why Now -->
        <div class="slide">
            <h2>The future of work is collaborative</h2>
            <h2 style="color: #FFD700;">and requires high emotional intelligence.</h2>
            <ul class="bullet-points">
                <li><strong>Remote/hybrid work</strong> makes interpersonal skills even more critical and harder to observe naturally</li>
                <li><strong>Gen Z values authenticity</strong> and seeks supportive digital environments for growth</li>
                <li><strong>AI advancement</strong> makes human skills more valuable, not less</li>
                <li><strong>Workplace mental health</strong> is now a priority for organizations</li>
            </ul>
            <p style="margin-top: 30px;">The convergence of these trends creates the perfect moment for AskFora.</p>
        </div>

        <!-- Slide 11: The Vision -->
        <div class="slide">
            <h2 style="color: #FFD700;">Our vision is to ensure everyone</h2>
            <h2>enters the workforce with the confidence</h2>
            <h2>and competence to succeed.</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">🏫</div>
                    <div class="stat-label">Partnerships with schools and universities</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">🏢</div>
                    <div class="stat-label">Corporate training modules for new hires</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">🏆</div>
                    <div class="stat-label">Advanced skill development</div>
                </div>
            </div>
            <p>We're building the infrastructure for professional development in the age of AI.</p>
        </div>

        <!-- Slide 12: The Ask -->
        <div class="slide">
            <h2 style="color: #FFD700;">Join us in building the future</h2>
            <h2>of professional development.</h2>
            <p style="font-size: 1.4rem; margin: 40px 0;">We're doing customer development with Gen Z students entering the workforce.</p>
            <div class="contact-info">
                <h4>Ready to get involved?</h4>
                <p><strong>Founder:</strong> Omer Trajman</p>
                <p><strong>LinkedIn:</strong> <a href='https://linkedin.com/in/omert'>linkedin.com/in/omert</a></p>
                <p><strong>Email:</strong> <a href='mailto:<EMAIL>'><EMAIL></a></p>
            </div>
            <p style="margin-top: 30px; font-size: 1.3rem;">Let's give the next generation the tools they need to thrive in their careers from day one.</p>
            <div class="logo">
                <img src="/icons/icon-640m.png" alt="AskFora Logo">
            </div>
        </div>
    </div>

    <!-- Scroll Arrow -->
    <div class="scroll-arrow" id="scrollArrow">
        <svg viewBox="0 0 24 24">
            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
        </svg>
    </div>

    <script>
        const slides = document.querySelectorAll('.slide');
        const scrollArrow = document.getElementById('scrollArrow');
        let currentSlideIndex = 0;

        // Track which phone widgets have been scrolled
        const phoneScrollStates = new Map();

        // Intersection Observer for fade-in/fade-out animations with scroll buffer
        const observerOptions = {
            threshold: [0, 0.1, 0.5, 0.9, 1],
            rootMargin: '20% 0px -20% 0px' // Add buffer zones
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const slideIndex = Array.from(slides).indexOf(entry.target);

                if (entry.intersectionRatio > 0.5) {
                    // Slide is prominently visible
                    entry.target.classList.add('visible');
                    entry.target.classList.remove('fade-out');
                    currentSlideIndex = slideIndex;
                } else if (entry.intersectionRatio > 0.1 && entry.intersectionRatio <= 0.5) {
                    // Slide is partially visible
                    entry.target.classList.add('visible');
                    entry.target.classList.remove('fade-out');
                } else if (entry.intersectionRatio <= 0.1 && entry.target.classList.contains('visible')) {
                    // Slide is being scrolled past
                    entry.target.classList.add('fade-out');
                }
            });

            // Update scroll arrow visibility
            updateScrollArrow();
        }, observerOptions);

        // Observe all slides
        slides.forEach((slide, index) => {
            if (index === 0) {
                slide.classList.add('visible'); // First slide starts visible
            }
            observer.observe(slide);
        });

        // Phone scroll detection and overlay management
        function setupPhoneScrollDetection() {
            const phoneContainers = document.querySelectorAll('.phone-container[data-phone-id]');

            phoneContainers.forEach(phoneContainer => {
                const phoneId = phoneContainer.getAttribute('data-phone-id');
                const chatContainer = phoneContainer.querySelector('.chat-container');
                const overlay = phoneContainer.querySelector('.scroll-overlay');

                if (chatContainer && overlay) {
                    // Initialize scroll state
                    phoneScrollStates.set(phoneId, false);

                    // Add scroll event listener
                    chatContainer.addEventListener('scroll', () => {
                        const scrollTop = chatContainer.scrollTop;
                        const scrollHeight = chatContainer.scrollHeight;
                        const clientHeight = chatContainer.clientHeight;

                        // Check if user has scrolled significantly (more than 50px or reached bottom)
                        const hasScrolled = scrollTop > 50 || (scrollTop + clientHeight >= scrollHeight - 10);

                        if (hasScrolled && !phoneScrollStates.get(phoneId)) {
                            phoneScrollStates.set(phoneId, true);
                            overlay.classList.add('hidden');
                            updateScrollArrow();
                        }
                    });
                }
            });
        }

        // Scroll arrow functionality - now considers phone scroll states
        function updateScrollArrow() {
            const isLastSlide = currentSlideIndex >= slides.length - 1;

            // Check if current slide has phone widgets that need to be scrolled
            const currentSlide = slides[currentSlideIndex];
            const phonesInCurrentSlide = currentSlide.querySelectorAll('.phone-container[data-phone-id]');

            let allPhonesScrolled = true;
            phonesInCurrentSlide.forEach(phone => {
                const phoneId = phone.getAttribute('data-phone-id');
                if (!phoneScrollStates.get(phoneId)) {
                    allPhonesScrolled = false;
                }
            });

            // Hide arrow if it's the last slide OR if current slide has unscrolled phones
            if (isLastSlide || (phonesInCurrentSlide.length > 0 && !allPhonesScrolled)) {
                scrollArrow.classList.add('hidden');
            } else {
                scrollArrow.classList.remove('hidden');
            }
        }

        scrollArrow.addEventListener('click', () => {
            if (currentSlideIndex < slides.length - 1) {
                const nextSlide = slides[currentSlideIndex + 1];
                nextSlide.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });

        // Enable scrolling for chat containers
        const chatContainers = document.querySelectorAll('.chat-container');
        chatContainers.forEach(container => {
            container.style.scrollBehavior = 'smooth';
        });

        // Initialize everything
        setupPhoneScrollDetection();
        updateScrollArrow();
    </script>
</body>
</html>